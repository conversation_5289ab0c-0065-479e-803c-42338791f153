<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-21T09:07:07.728255" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal">
<suite id="s1-s1" name="RAC29a-TC-220 - Validate Accessing the ATM Details Screen- on ATM Details" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-220_-_Validate_Accessing_the_ATM_Details_Screen-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.748685" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.748685" elapsed="0.000926"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.748685" elapsed="0.000926"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750625" elapsed="0.000135"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.749611" elapsed="0.001149"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:11.745682" elapsed="0.005078"/>
</kw>
<test id="s1-s1-t1" name="Validate Accessing the ATM Details Screen- on ATM Details" line="34">
<kw name="Validate Accessing ATM Details Page">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.750760" level="INFO">Set test documentation to:
Validate Accessing ATM Details Page</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.756857" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="0.006097"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:07:11.756857" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:07:11.756857" elapsed="0.001058"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.758864" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T09:07:11.758864" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T09:07:11.758864" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:07:11.758864" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:11.758864" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:07:11.758864" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.759865" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:11.758864" elapsed="0.001001"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:11.758864" elapsed="0.001001"/>
</branch>
<status status="PASS" start="2025-06-21T09:07:11.758864" elapsed="0.001001"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T09:07:11.780654" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-21T09:07:11.781585" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T09:07:11.759865" elapsed="0.021720"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.782012" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.783041" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="0.001029"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="0.001029"/>
</branch>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="0.001029"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T09:07:11.783041" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T09:07:11.783041" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:07:11.783041" elapsed="0.000998"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T09:07:11.943346" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T09:07:12.633256" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-21T09:07:12.633256" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T09:07:11.784141" elapsed="0.849115"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.633256" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:12.633256" elapsed="0.005538"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:12.633256" elapsed="0.005538"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="0.856782"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CB1A390&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CB1A9F0&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T09:07:12.638794" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:12.638794" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:07:12.638794" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="3.589701"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="3.589701"/>
</branch>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="3.589701"/>
</if>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="3.589701"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.228495" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.229494" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.230493" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.230493" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.230493" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.230493" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.230493" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:07:16.231494" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:07:16.228495" elapsed="0.002999"/>
</branch>
<status status="PASS" start="2025-06-21T09:07:12.638794" elapsed="3.592700"/>
</if>
<status status="PASS" start="2025-06-21T09:07:11.782012" elapsed="4.449482"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T09:07:16.232493" elapsed="0.029079"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:16.261572" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T09:07:16.261572" elapsed="5.924961"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:16.261572" elapsed="5.924961"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:07:32.187859" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:07:22.187530" elapsed="10.000329"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:32.187859" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:07:32.217262" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:32.187859" elapsed="0.029403"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:07:32.217262" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:07:42.217998" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:07:32.217262" elapsed="10.000736"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:42.217998" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:07:42.250960" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:42.217998" elapsed="0.032962"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:07:42.251970" elapsed="0.000997"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:07:47.255677" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:07:42.253971" elapsed="5.001706"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:47.255677" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:07:47.284633" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:47.255677" elapsed="0.028956"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:07:47.284633" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T09:07:16.232493" elapsed="31.052140"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:47.411727" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:07:47.284633" elapsed="0.127094"/>
</kw>
<msg time="2025-06-21T09:07:47.411727" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:07:47.284633" elapsed="0.127094"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:47.411727" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:47.411727" elapsed="0.251957"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:07:47.663684" elapsed="0.060280"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:47.724966" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:47.723964" elapsed="0.322400"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:48.046364" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:48.046364" elapsed="0.238206"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:07:50.295391" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:07:48.284570" elapsed="2.010821"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:55.235668" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1717.png"&gt;&lt;img src="selenium-screenshot-1717.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:07:55.235668" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:07:50.296772" elapsed="5.028571">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-21T09:07:55.325343" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:07:50.296772" elapsed="5.028571"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:47.411727" elapsed="7.913616"/>
</iter>
<status status="PASS" start="2025-06-21T09:07:47.411727" elapsed="7.913616"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:07:55.325343" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:56.150862" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1718.png"&gt;&lt;img src="selenium-screenshot-1718.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-21T09:07:55.325343" elapsed="0.825519"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-21T09:07:11.757915" elapsed="44.392947"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:07:11.757915" elapsed="44.392947"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="44.400102"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:07:56.150862" elapsed="0.071920"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-21T09:07:56.222782" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:07:56.222782" elapsed="3.782491"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:00.006307" elapsed="0.002561"/>
</kw>
<status status="PASS" start="2025-06-21T09:07:56.150862" elapsed="3.858999"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:00.012933" elapsed="0.066865"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:00.079798" elapsed="0.049750"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.229225" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.129548" elapsed="0.100669"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:00.231212" elapsed="0.031882"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.371545" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.263094" elapsed="0.108451"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.450416" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.371545" elapsed="0.078871"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.547144" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.450416" elapsed="0.096728"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.639693" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.547144" elapsed="0.092549"/>
</kw>
<msg time="2025-06-21T09:08:00.639693" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:00.547144" elapsed="0.092549"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.731787" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.639693" elapsed="0.092094"/>
</kw>
<msg time="2025-06-21T09:08:00.731787" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:00.639693" elapsed="0.092094"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.808244" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.731787" elapsed="0.076457"/>
</kw>
<msg time="2025-06-21T09:08:00.808244" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:00.731787" elapsed="0.076457"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:00.808244" elapsed="0.003646"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:00.009861" elapsed="0.802029"/>
</kw>
<arg>Validate Accessing ATM Details Page</arg>
<arg>VMS_UAT</arg>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="49.061130"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:00.815346" elapsed="0.048077"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:00.863423" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:00.863423" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:00.863423" elapsed="0.221364"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:04.085411" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:01.084787" elapsed="3.000624"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-21T09:08:04.085411" elapsed="4.246853"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:00.813914" elapsed="7.518350"/>
</kw>
<doc>Validate Accessing ATM Details Page</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-21T09:07:11.750760" elapsed="56.581504"/>
</test>
<doc>Validate Details of All ATMs</doc>
<status status="PASS" start="2025-06-21T09:07:07.831665" elapsed="60.500599"/>
</suite>
<suite id="s1-s2" name="RAC29a-TC-221 - Validate Viewing ATM Device Details- on ATM Details" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-221_-_Validate_Viewing_ATM_Device_Details-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.835982" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.835982" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.847999" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.847999" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.012017"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.847999" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:08.847999" elapsed="0.004028"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.847999" elapsed="0.004028"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.852027" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.852027" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.852027" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.852027" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.852027" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.852027" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:08.835982" elapsed="0.016045"/>
</kw>
<test id="s1-s2-t1" name="Validate Accessing ATM Details Page" line="36">
<kw name="Validate Accessing ATM Details Page">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.868051" level="INFO">Set test documentation to:
Validate Accessing ATM Details Page</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T09:08:08.868051" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.874261" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:08:08.874261" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:08.874261" elapsed="0.004868"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:08.879129" elapsed="0.002927"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:08.885021" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T09:08:08.885021" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T09:08:08.885021" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.885021" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T09:08:08.915918" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-21T09:08:08.915918" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="0.030897"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.915918" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:08:08.915918" elapsed="0.010768"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.926686" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:08.926686" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:08.926686" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:08:08.926686" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T09:08:08.926686" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T09:08:08.926686" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:08.926686" elapsed="0.005032"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T09:08:09.104922" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T09:08:09.779186" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-21T09:08:09.793714" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T09:08:08.931718" elapsed="0.861996"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.794626" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:09.794626" elapsed="0.001031"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:09.793714" elapsed="0.001943"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T09:08:08.915918" elapsed="0.880103"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBBC4D0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBBD220&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T09:08:09.796021" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:09.796021" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:08:09.806924" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="2.705944"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="2.705944"/>
</branch>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="2.705944"/>
</if>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="2.705944"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.501965" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.501965" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.506330" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.506330" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:08:12.501965" elapsed="0.004365"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:08:12.501965" elapsed="0.004365"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.506330" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.506330" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.507538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.509060" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.509060" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.509060" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:12.509060" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:08:12.501965" elapsed="0.007095"/>
</branch>
<status status="PASS" start="2025-06-21T09:08:09.796021" elapsed="2.713039"/>
</if>
<status status="PASS" start="2025-06-21T09:08:08.915918" elapsed="3.593142"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T09:08:12.509060" elapsed="0.044497"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:12.558421" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T09:08:12.557539" elapsed="2.640975"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:12.554233" elapsed="2.644281"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:25.199102" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:15.198514" elapsed="10.000588"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:25.199102" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:25.248515" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:25.199102" elapsed="0.049413"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:08:25.248515" elapsed="0.006099"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:35.254916" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:25.254614" elapsed="10.000302"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:35.255954" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:35.279542" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:35.255954" elapsed="0.023588"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:08:35.279542" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:40.279706" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:35.279542" elapsed="5.000164"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:40.279706" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:40.314556" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:40.279706" elapsed="0.034850"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:08:40.314556" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T09:08:12.509060" elapsed="27.805496"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:40.384890" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:40.314556" elapsed="0.070334"/>
</kw>
<msg time="2025-06-21T09:08:40.384890" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:40.314556" elapsed="0.070334"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:40.384890" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:40.384890" elapsed="0.236403"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:40.621293" elapsed="0.048051"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:40.671375" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:40.670376" elapsed="0.358279"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:41.029479" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:41.029479" elapsed="0.220167"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:43.249928" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:41.249646" elapsed="2.000282"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:44.269209" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1719.png"&gt;&lt;img src="selenium-screenshot-1719.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:08:44.270208" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:08:43.251476" elapsed="1.022789">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-21T09:08:44.275257" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:43.250456" elapsed="1.024801"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:40.384890" elapsed="3.890367"/>
</iter>
<status status="PASS" start="2025-06-21T09:08:40.384890" elapsed="3.891369"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:44.277257" elapsed="0.001129"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:45.297873" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1720.png"&gt;&lt;img src="selenium-screenshot-1720.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-21T09:08:44.279035" elapsed="1.018838"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-21T09:08:08.885021" elapsed="36.415318"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:08:08.882056" elapsed="36.418283"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-21T09:08:08.868051" elapsed="36.432288"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:45.300339" elapsed="0.059215"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:45.362621" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:45.362030" elapsed="2.817594"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:48.180632" elapsed="0.001011"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:45.300339" elapsed="2.881304"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:48.183628" elapsed="0.056773"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:48.241922" elapsed="0.050038"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.383622" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.292945" elapsed="0.090677"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:48.384646" elapsed="0.042885"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.538075" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.427531" elapsed="0.110544"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.620384" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.538075" elapsed="0.082309"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.711657" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.620384" elapsed="0.091273"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.822116" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.711657" elapsed="0.110459"/>
</kw>
<msg time="2025-06-21T09:08:48.822116" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:48.711657" elapsed="0.110459"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.900733" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.822116" elapsed="0.078617"/>
</kw>
<msg time="2025-06-21T09:08:48.900733" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:48.822116" elapsed="0.078617"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:48.996382" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:08:48.900733" elapsed="0.095649"/>
</kw>
<msg time="2025-06-21T09:08:48.996382" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:08:48.900733" elapsed="0.095649"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:48.996382" elapsed="0.004537"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:48.182625" elapsed="0.818294"/>
</kw>
<kw name="Then The user verifies ATM Details are present" owner="ATMDetails">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:50.713152" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1721.png"&gt;&lt;img src="selenium-screenshot-1721.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:08:50.714511" level="FAIL">Page should have contained text 'xpath=//table[@class='table gs-table' and .//thead[@class='gs-table-head']//tr//th//span[@class='gs-button' and text()='ATM Number']]//thead//tr' but did not.</msg>
<arg>${TABLE_XPATH}//thead//tr</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-21T09:08:49.000919" elapsed="1.714293">Page should have contained text 'xpath=//table[@class='table gs-table' and .//thead[@class='gs-table-head']//tr//th//span[@class='gs-button' and text()='ATM Number']]//thead//tr' but did not.</status>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>${TABLE_XPATH}//tbody//tr</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-06-21T09:08:50.715212" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2025-06-21T09:08:49.000919" elapsed="1.714293">Page should have contained text 'xpath=//table[@class='table gs-table' and .//thead[@class='gs-table-head']//tr//th//span[@class='gs-button' and text()='ATM Number']]//thead//tr' but did not.</status>
</kw>
<arg>Validate Accessing ATM Details Page</arg>
<arg>VMS_UAT</arg>
<status status="FAIL" start="2025-06-21T09:08:08.868051" elapsed="41.847161">Page should have contained text 'xpath=//table[@class='table gs-table' and .//thead[@class='gs-table-head']//tr//th//span[@class='gs-button' and text()='ATM Number']]//thead//tr' but did not.</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:08:50.715212" elapsed="0.046178"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:08:50.761390" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:08:50.776413" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:08:50.776413" elapsed="2.291328"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:08:56.069635" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:08:53.067741" elapsed="3.001894"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-21T09:08:56.069635" elapsed="4.792785"/>
</kw>
<status status="PASS" start="2025-06-21T09:08:50.715212" elapsed="10.148180"/>
</kw>
<doc>Validate Accessing ATM Details Page</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-21T09:08:08.868051" elapsed="51.996274">Page should have contained text 'xpath=//table[@class='table gs-table' and .//thead[@class='gs-table-head']//tr//th//span[@class='gs-button' and text()='ATM Number']]//thead//tr' but did not.</status>
</test>
<doc>Validate Details of All ATMs</doc>
<status status="FAIL" start="2025-06-21T09:08:08.332264" elapsed="52.532061"/>
</suite>
<suite id="s1-s3" name="RAC29a-TC-223 - Verify Last Update Date- on ATM Details" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-223_-_Verify_Last_Update_Date-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.223534" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.223534" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.223534" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.223534" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.223534" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.223534" elapsed="0.002393"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.225927" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.225927" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.225927" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.225927" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.225927" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.229089" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:01.229089" elapsed="0.000585"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.229089" elapsed="0.000585"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.230220" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:01.230220" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.229674" elapsed="0.000546"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T09:09:01.230220" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:01.231372" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:01.230220" elapsed="0.001267"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:01.230220" elapsed="0.001267"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.232071" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:01.232071" elapsed="0.000463"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.231702" elapsed="0.000832"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:01.223534" elapsed="0.009157"/>
</kw>
<test id="s1-s3-t1" name="Verify Update From Gasper Button Functionality" line="37">
<kw name="Verify Update From Gasper Button Functionality">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.233717" level="INFO">Set test documentation to:
Verify Update From Gasper Button Functionality</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T09:09:01.233717" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.234717" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:09:01.233717" elapsed="0.001084"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:01.234801" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:01.234801" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.237380" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T09:09:01.236348" elapsed="0.001032">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T09:09:01.237380" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:01.236348" elapsed="0.001032"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:01.237380" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:01.237380" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.238377" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:01.237380" elapsed="0.000997"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:01.237380" elapsed="0.000997"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:01.237380" elapsed="0.000997"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T09:09:01.241611" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-21T09:09:01.241611" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T09:09:01.238377" elapsed="0.003234"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.247494" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:09:01.247090" elapsed="0.000404"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.248207" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:01.248207" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:01.248003" elapsed="0.000204"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:01.248003" elapsed="0.000204"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T09:09:01.248207" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T09:09:01.248207" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:01.248207" elapsed="0.001025"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T09:09:01.418395" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T09:09:02.153083" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-21T09:09:02.153083" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T09:09:01.249232" elapsed="0.903851"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.153083" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:02.153083" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:02.153083" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T09:09:01.245169" elapsed="0.907914"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:02.153083" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T09:09:02.153083" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:09:02.161180" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:09:02.161180" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:09:02.161180" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:09:02.161180" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:02.161787" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:02.161787" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.163411" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:02.162947" elapsed="0.000464"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T09:09:02.163411" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBFDA30&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T09:09:02.163411" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T09:09:02.164056" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:02.164056" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:02.164056" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.164056" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBFEFC0&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:09:02.164056" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.165083" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:02.165083" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.165083" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:02.165083" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.165083" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:02.165083" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T09:09:02.165083" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T09:09:02.165083" elapsed="0.000998"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T09:09:02.166081" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:02.166081" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:09:02.167039" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T09:09:02.166081" elapsed="2.669362"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:02.164056" elapsed="2.671387"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:02.164056" elapsed="2.671387"/>
</if>
<status status="PASS" start="2025-06-21T09:09:02.162814" elapsed="2.672629"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:04.835443" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:02.162814" elapsed="2.672629"/>
</if>
<status status="PASS" start="2025-06-21T09:09:01.245169" elapsed="3.590274"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T09:09:04.835443" elapsed="0.066054"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:04.907114" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T09:09:04.907114" elapsed="5.027565"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:04.901497" elapsed="5.034185"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:09:19.937078" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:09:09.936683" elapsed="10.000395"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:19.937078" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:19.971887" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:19.937078" elapsed="0.034809"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:09:19.971887" elapsed="0.005031"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:09:29.977345" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:09:19.976918" elapsed="10.000427"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:29.977345" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:30.008381" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:29.977345" elapsed="0.031036"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:09:30.008381" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:09:35.009025" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:09:30.008381" elapsed="5.000644"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:35.009025" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:35.036358" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:35.009025" elapsed="0.027333"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:09:35.052024" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T09:09:04.835443" elapsed="30.216581"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:35.114675" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:35.053652" elapsed="0.061023"/>
</kw>
<msg time="2025-06-21T09:09:35.114675" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:35.053652" elapsed="0.061023"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:35.114675" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:35.114675" elapsed="0.251465"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:35.379851" elapsed="0.049078"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:35.428929" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:35.428929" elapsed="0.318280"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:35.747209" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:35.747209" elapsed="4.103725"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:09:41.853256" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:09:39.851934" elapsed="2.001322"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:42.536867" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1722.png"&gt;&lt;img src="selenium-screenshot-1722.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:09:42.537874" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:09:41.853256" elapsed="0.687581">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-21T09:09:42.541825" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:41.853256" elapsed="0.688569"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:35.114675" elapsed="7.427150"/>
</iter>
<status status="PASS" start="2025-06-21T09:09:35.114675" elapsed="7.428148"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:42.542823" elapsed="0.001043"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:43.233955" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1723.png"&gt;&lt;img src="selenium-screenshot-1723.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-21T09:09:42.544827" elapsed="0.689128"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-21T09:09:01.236348" elapsed="41.997607"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:01.234801" elapsed="42.000122"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-21T09:09:01.233717" elapsed="42.001206"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:43.236966" elapsed="0.055938"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:43.292904" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:43.292904" elapsed="3.378475"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:46.672367" elapsed="0.001005"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:43.235897" elapsed="3.437475"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:46.675928" elapsed="0.050555"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:46.726483" elapsed="0.047480"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:46.858379" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:46.774982" elapsed="0.084396"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:46.860404" elapsed="0.050856"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:46.990979" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:46.912258" elapsed="0.078721"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:47.075354" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:46.990979" elapsed="0.084375"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:47.164466" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:47.085963" elapsed="0.078503"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:47.258784" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:47.164466" elapsed="0.094318"/>
</kw>
<msg time="2025-06-21T09:09:47.258784" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:47.164466" elapsed="0.094318"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:47.337541" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:47.258784" elapsed="0.078757"/>
</kw>
<msg time="2025-06-21T09:09:47.337541" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:47.258784" elapsed="0.078757"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:47.431820" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:09:47.337541" elapsed="0.094279"/>
</kw>
<msg time="2025-06-21T09:09:47.431820" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:47.337541" elapsed="0.094279"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:47.431820" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:46.674896" elapsed="0.756924"/>
</kw>
<kw name="Then The user validates ATM Last Update" owner="ATMDetails">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:48.963003" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1724.png"&gt;&lt;img src="selenium-screenshot-1724.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:09:48.963003" level="FAIL">Page should have contained text 'xpath=//*[@id="MainContent_lblGasper"]' but did not.</msg>
<arg>xpath=//*[@id="MainContent_lblGasper"]</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-21T09:09:47.431820" elapsed="1.531183">Page should have contained text 'xpath=//*[@id="MainContent_lblGasper"]' but did not.</status>
</kw>
<status status="FAIL" start="2025-06-21T09:09:47.431820" elapsed="1.531183">Page should have contained text 'xpath=//*[@id="MainContent_lblGasper"]' but did not.</status>
</kw>
<arg>Verify Update From Gasper Button Functionality</arg>
<arg>VMS_UAT</arg>
<status status="FAIL" start="2025-06-21T09:09:01.232691" elapsed="47.730312">Page should have contained text 'xpath=//*[@id="MainContent_lblGasper"]' but did not.</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:09:48.963003" elapsed="0.063146"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:49.026149" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:09:49.026149" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:09:49.026149" elapsed="0.270513"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:09:52.298423" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:09:49.297989" elapsed="3.000434"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-21T09:09:52.298423" elapsed="6.734296"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:48.963003" elapsed="10.069716"/>
</kw>
<doc>Verify Update From Gasper Button Functionality</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-21T09:09:01.232691" elapsed="57.800028">Page should have contained text 'xpath=//*[@id="MainContent_lblGasper"]' but did not.</status>
</test>
<doc>Verify Update From Gasper Button Functionality</doc>
<status status="FAIL" start="2025-06-21T09:09:00.879129" elapsed="58.153590"/>
</suite>
<suite id="s1-s4" name="RAC29a-TC-224 Validate Search - ATM Number Column - on ATM Details" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-224_Validate_Search_-_ATM_Number_Column_-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.166294" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.165290" elapsed="0.001004"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.165290" elapsed="0.001004"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.166294" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.166294" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.166294" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.167289" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.167289" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.167289" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.168286" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:59.167289" elapsed="0.000997"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.167289" elapsed="0.001091"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:59.165007" elapsed="0.003373"/>
</kw>
<test id="s1-s4-t1" name="Validate Search - ATM Number Coloumn - on ATM Details" line="40">
<kw name="Validate Search - ATM Number Coloumn - on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.168380" level="INFO">Set test documentation to:
Validate Search - ATM Number Coloumn - on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.172967" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="0.004587"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:59.172967" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:59.172967" elapsed="0.001027"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.174995" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T09:09:59.174995" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T09:09:59.174995" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:09:59.173994" elapsed="0.001001"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T09:09:59.174995" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:09:59.174995" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.175991" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:59.174995" elapsed="0.000996"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:59.174995" elapsed="0.000996"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:59.174995" elapsed="0.000996"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T09:09:59.181107" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-21T09:09:59.181107" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T09:09:59.175991" elapsed="0.005116"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.182103" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:09:59.182103" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.182103" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:09:59.182103" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:09:59.182103" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:09:59.182103" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T09:09:59.183104" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T09:09:59.183104" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:09:59.183104" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T09:09:59.390599" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T09:10:00.409537" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-21T09:10:00.409537" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T09:09:59.183104" elapsed="1.226433"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T09:09:59.182103" elapsed="1.227434"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBB4C50&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CC24C50&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T09:10:00.409537" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:00.409537" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:10:00.409537" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="3.554501"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="3.554501"/>
</branch>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="3.554501"/>
</if>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="3.554501"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:10:03.964038" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:10:00.409537" elapsed="3.554501"/>
</if>
<status status="PASS" start="2025-06-21T09:09:59.181107" elapsed="4.782931"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T09:10:03.964038" elapsed="0.034958"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:03.998996" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T09:10:03.998996" elapsed="3.061259"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:10:03.998996" elapsed="3.061259"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:17.061094" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:07.060255" elapsed="10.000839"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:17.061094" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:17.076748" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:17.061094" elapsed="0.015654"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:10:17.092392" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:27.093026" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:17.092392" elapsed="10.000634"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:27.093026" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:27.112757" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:27.093026" elapsed="0.019731"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:10:27.112757" elapsed="0.011106"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:32.126803" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:27.123863" elapsed="5.002940"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:32.128028" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:32.176180" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:32.128028" elapsed="0.048152"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:10:32.176180" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T09:10:03.964038" elapsed="28.212142"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:32.235342" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:32.176180" elapsed="0.059162"/>
</kw>
<msg time="2025-06-21T09:10:32.235342" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:10:32.176180" elapsed="0.059162"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:32.235342" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:32.235342" elapsed="0.227375"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:32.463402" elapsed="0.041603"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:32.506004" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:32.505005" elapsed="0.349122"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:32.854127" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:32.854127" elapsed="0.225501"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:35.081281" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:33.079913" elapsed="2.001368"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:37.085125" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1725.png"&gt;&lt;img src="selenium-screenshot-1725.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-21T09:10:37.086126" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:10:35.082649" elapsed="2.006546">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-21T09:10:37.089195" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:10:35.082649" elapsed="2.006546"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:32.235342" elapsed="4.853853"/>
</iter>
<status status="PASS" start="2025-06-21T09:10:32.235342" elapsed="4.854828"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:37.090170" elapsed="0.000993"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:37.854842" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1726.png"&gt;&lt;img src="selenium-screenshot-1726.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-21T09:10:37.092125" elapsed="0.762717"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-21T09:09:59.173994" elapsed="38.681863"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:09:59.173994" elapsed="38.681863"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="38.687477"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:37.857837" elapsed="0.061903"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:37.919740" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:37.919740" elapsed="3.137038"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:41.056778" elapsed="0.001065"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:37.856850" elapsed="3.200993"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:41.059865" elapsed="0.050237"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:41.110102" elapsed="0.058693"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.276769" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.169798" elapsed="0.106971"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:41.277768" elapsed="0.050618"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.423953" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.328386" elapsed="0.095567"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.535354" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.423953" elapsed="0.111401"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.646788" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.535354" elapsed="0.111434"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.749342" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.650457" elapsed="0.098885"/>
</kw>
<msg time="2025-06-21T09:10:41.749342" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:10:41.650457" elapsed="0.098885"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.851794" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.749342" elapsed="0.102452"/>
</kw>
<msg time="2025-06-21T09:10:41.851794" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:10:41.749342" elapsed="0.102452"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.946740" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.851794" elapsed="0.094946"/>
</kw>
<msg time="2025-06-21T09:10:41.946740" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:10:41.851794" elapsed="0.094946"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:41.946740" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:41.058867" elapsed="0.887873"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:41.946740" level="INFO">Clicking element 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:41.946740" elapsed="0.261375"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked Search Element</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:42.208115" elapsed="0.001864"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:47.212159" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:42.209979" elapsed="5.002180"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:47.212159" level="INFO">Typing text 'S08003' into text field 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<arg>${SEARCH_KEY}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:47.212159" elapsed="0.271843"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has input Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:47.484002" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>${SEARCH_KEY}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-06-21T09:10:47.484002" elapsed="0.063113"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>-------------------------- The user has waited and found the Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:47.547115" elapsed="0.013599"/>
</kw>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-21T09:10:41.946740" elapsed="5.613974"/>
</kw>
<kw name="Then The user verifies only one row is data present" owner="ATMDetails">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.561117" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:47.560714" elapsed="3.000403"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:50.591295" level="INFO">${datarows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="1e67549c698faca5373c9f1c423b5c9d", element="f.287EF9AB281AAAA644CC7715A58DA139.d.52A5B233ED48C1313EDBDEBB710A9691.e.200")&gt;]</msg>
<var>${datarows}</var>
<arg>//*[@id="root"]/div/table//tbody//tr</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.561117" elapsed="0.030178"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.591295" level="INFO">Length is 1.</msg>
<msg time="2025-06-21T09:10:50.591295" level="INFO">${row_count} = 1</msg>
<var>${row_count}</var>
<arg>${datarows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-21T09:10:50.591295" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.591295" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${row_count}</arg>
<arg>1</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-06-21T09:10:50.591295" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has verified the default number of rows</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:50.591295" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:47.560714" elapsed="3.030581"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:50.591295" elapsed="0.062967"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Column Element Has Been Found: ${COLUMN}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:50.654262" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:50.713249" level="INFO">${header_element_text} = ATM Number</msg>
<var>${header_element_text}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.654262" elapsed="0.058987"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.713249" level="INFO">${cleaned_header_element_text} = ATMNumber</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:10:50.713249" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-21T09:10:50.728908" level="INFO">${cleaned_h_element_text} = ATMNumber</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.728908" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:50.728908" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.728908" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:50.728908" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:50.728908" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:50.763856" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.728908" elapsed="0.034948"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:50.763856" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.776747" level="INFO">${header_xpath} = xpath=//*[@id="root"]/div/table/thead/tr/th[1]/span</msg>
<var>${header_xpath}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th[${i + 1}]/span</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:50.763856" elapsed="0.012891"/>
</kw>
<kw name="Run Keyword And Return" owner="BuiltIn">
<kw name="Get Text With Retry" owner="ATMDetails">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:10:50.776747" level="INFO">${max_retries} = 3</msg>
<var>${max_retries}</var>
<arg>3</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:10:50.776747" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Get Text" owner="SeleniumLibrary">
<arg>${xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.786287" elapsed="0.054236"/>
</kw>
<msg time="2025-06-21T09:10:50.840523" level="INFO">${status} = PASS</msg>
<msg time="2025-06-21T09:10:50.840523" level="INFO">${text} = ATM Number</msg>
<var>${status}</var>
<var>${text}</var>
<arg>Get Text</arg>
<arg>${xpath}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-06-21T09:10:50.786287" elapsed="0.054236"/>
</kw>
<if>
<branch type="IF" condition="&quot;${status}&quot; == &quot;PASS&quot;">
<return>
<value>${text}</value>
<status status="PASS" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</if>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Retry ${retry + 1} for getting text from ${xpath}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<var name="${retry}">0</var>
<status status="PASS" start="2025-06-21T09:10:50.776747" elapsed="0.063776"/>
</iter>
<var>${retry}</var>
<value>${max_retries}</value>
<status status="PASS" start="2025-06-21T09:10:50.776747" elapsed="0.063776"/>
</for>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${text}</var>
<arg>${xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<return>
<value>${text}</value>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</return>
<arg>${header_xpath}</arg>
<doc>Get text from element with retry logic to handle stale element references</doc>
<status status="PASS" start="2025-06-21T09:10:50.776747" elapsed="0.063776"/>
</kw>
<msg time="2025-06-21T09:10:50.840523" level="INFO">Returning from the enclosing user keyword.</msg>
<var>${header_text}</var>
<arg>Get Text With Retry</arg>
<arg>${header_xpath}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:10:50.776747" elapsed="0.063776"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:10:50.840523" elapsed="0.000000"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-21T09:10:50.763856" elapsed="0.076667"/>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="PASS" start="2025-06-21T09:10:50.763856" elapsed="0.092610"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Column with text '${COLUMN}' not found.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${num_elements}</var>
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>False</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Set Variable" owner="BuiltIn">
<var>${element_xpath}</var>
<arg>xpath=//tbody//tr[${e + 1}]//td[${index} + 1]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${element_xpath}</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Get Text With Retry" owner="ATMDetails">
<var>${element_text}</var>
<arg>${element_xpath}</arg>
<doc>Get text from element with retry logic to handle stale element references</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Search Result Retrieved: ${element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${element_t}</var>
<arg>${element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${search_key}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${SEARCH_KEY}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${search_k}</var>
<arg>${search_key}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.009173"/>
</kw>
<if>
<branch type="IF" condition="$element_t == $search_k">
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------- Search Key was found in the correct column.</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</if>
<var name="${e}"/>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.009173"/>
</iter>
<var>${e}</var>
<value>${num_elements}</value>
<status status="NOT RUN" start="2025-06-21T09:10:50.856466" elapsed="0.009173"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${found}' == 'False'</arg>
<arg>Fail</arg>
<arg>Search Key was NOT found in the correct column.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-21T09:10:50.865639" elapsed="0.000000"/>
</kw>
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-21T09:10:50.591295" elapsed="0.274344"/>
</kw>
<arg>Validate Search - ATM Number Coloumn - on ATM Details</arg>
<arg>VMS_UAT</arg>
<arg>S08003</arg>
<arg>ATM Number</arg>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="51.697259"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:10:50.872184" elapsed="0.047557"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:10:50.919741" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:10:50.919741" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:10:50.919741" elapsed="1.883266"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:10:55.805299" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:10:52.804031" elapsed="3.001268"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-21T09:10:55.805299" elapsed="4.362497"/>
</kw>
<status status="PASS" start="2025-06-21T09:10:50.865639" elapsed="9.302157"/>
</kw>
<doc>Validate Search - ATM Number Coloumn - on ATM Details</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-21T09:09:59.168380" elapsed="60.999416"/>
</test>
<doc>Validate Search - ATM Number Coloumn - on ATM Details</doc>
<status status="PASS" start="2025-06-21T09:09:59.032719" elapsed="61.135077"/>
</suite>
<suite id="s1-s5" name="RAC29a-TC-225 Validate Search - Serial Number Column - on ATM Details" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-225_Validate_Search_-_Serial_Number_Column_-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.674456" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.674456" elapsed="0.002393"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.672802" elapsed="0.004047"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.676849" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.676849" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.676849" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.676849" elapsed="0.012028"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.690523" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.688877" elapsed="0.001646"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.690523" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.690523" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.699531" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:00.699531" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.690523" elapsed="0.009008"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T09:11:00.700835" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:00.700835" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:11:00.700835" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:11:00.700835" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.706323" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:00.703773" elapsed="0.002550"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:00.700835" elapsed="0.005488"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:00.671779" elapsed="0.034544"/>
</kw>
<test id="s1-s5-t1" name="Validate Search - Serial Number Coloumn- on ATM Details" line="40">
<kw name="Validate Search - Serial Number Coloumn- on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.722267" level="INFO">Set test documentation to:
Validate Search - Serial Number Coloumn- on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T09:11:00.720848" elapsed="0.001419"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.738179" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:11:00.733192" elapsed="0.004987"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:11:00.738179" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:11:00.744573" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.754888" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T09:11:00.754888" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T09:11:00.754888" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.754888" level="INFO">${BASE_URL} = VMS_PROD</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T09:11:00.754888" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T09:11:00.794005" level="INFO">Property value fetched is:  https://vms.absa.africa/</msg>
<msg time="2025-06-21T09:11:00.794005" level="INFO">${base_url} = https://vms.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T09:11:00.754888" elapsed="0.039117"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.806313" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:11:00.800044" elapsed="0.006269"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.807636" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:00.807636" elapsed="0.002839"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:00.806313" elapsed="0.004162"/>
</branch>
<status status="PASS" start="2025-06-21T09:11:00.806313" elapsed="0.004162"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T09:11:00.811164" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T09:11:00.811164" elapsed="0.001440"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:11:00.814028" elapsed="0.003792"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T09:11:00.963654" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T09:11:01.700623" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-21T09:11:01.700623" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T09:11:00.817820" elapsed="0.882803"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.700623" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:01.700623" elapsed="0.004346"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:01.700623" elapsed="0.004346"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T09:11:00.800044" elapsed="0.904925"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T09:11:01.704969" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CBECB00&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.708046" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001529CC44E60&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.708046" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.708046" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.708046" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T09:11:01.708046" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:01.708046" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:11:01.708046" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="2.603842"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="2.603842"/>
</branch>
<status status="PASS" start="2025-06-21T09:11:01.708046" elapsed="2.603842"/>
</if>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="2.606919"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.312942" elapsed="0.001311"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:04.314345" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T09:11:04.311888" elapsed="0.002457"/>
</branch>
<status status="PASS" start="2025-06-21T09:11:01.704969" elapsed="2.609376"/>
</if>
<status status="PASS" start="2025-06-21T09:11:00.800044" elapsed="3.514301"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T09:11:04.316561" elapsed="0.038086"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:04.354647" level="INFO">Opening url 'https://vms.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T09:11:04.354647" elapsed="4.568662"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T09:11:04.354647" elapsed="4.569659"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:18.928321" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:08.924306" elapsed="10.004015"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:18.928321" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:18.974543" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:18.928321" elapsed="0.047248"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:11:18.976456" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:28.977164" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:18.976456" elapsed="10.000708"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:28.977164" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:28.994850" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:28.977164" elapsed="0.017686"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:11:28.994850" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:33.995161" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:28.994850" elapsed="5.000311"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:33.995161" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:34.033265" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:33.995161" elapsed="0.038104"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T09:11:34.034425" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T09:11:04.314345" elapsed="29.720080"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:34.097163" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:34.034425" elapsed="0.062738"/>
</kw>
<msg time="2025-06-21T09:11:34.101083" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:34.034425" elapsed="0.066658"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:34.103906" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:34.103906" elapsed="0.238872"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:34.343393" elapsed="0.054550"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:34.397943" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:34.397943" elapsed="0.415736"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:34.816533" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:34.815022" elapsed="0.231587"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:37.048996" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:35.047617" elapsed="2.001379"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:37.109916" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:37.048996" elapsed="0.060920"/>
</kw>
<msg time="2025-06-21T09:11:37.109916" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:37.048996" elapsed="0.060920"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:34.102784" elapsed="3.007132"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:37.109916" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:37.109916" elapsed="0.269807"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:37.380557" elapsed="0.051334"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:37.432448" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:37.432448" elapsed="0.343319"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:37.775767" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:37.775767" elapsed="0.218757"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:39.995768" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:37.994524" elapsed="2.001244"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:40.061117" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:39.996784" elapsed="0.064333"/>
</kw>
<msg time="2025-06-21T09:11:40.061117" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:39.996784" elapsed="0.064333"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:37.109916" elapsed="2.951201"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:40.061117" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:40.061117" elapsed="0.245507"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:40.306624" elapsed="0.060155"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:40.368759" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:40.367762" elapsed="0.286643"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:40.663525" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:40.654405" elapsed="1.685508"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:44.341293" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:42.340936" elapsed="2.000357"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:44.398472" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:44.341293" elapsed="0.057179"/>
</kw>
<msg time="2025-06-21T09:11:44.407484" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:44.341293" elapsed="0.066191"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:40.061117" elapsed="4.346367"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:44.407484" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:44.407484" elapsed="0.232252"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T09:11:44.639736" elapsed="0.075552"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:44.715288" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:44.715288" elapsed="0.402422"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:45.117710" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T09:11:45.117710" elapsed="1.596268"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:11:48.716617" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:11:46.714985" elapsed="2.001632"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:50.346972" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Execution terminated by signal</msg>
<msg time="2025-06-21T09:11:50.346972" level="FAIL">InvalidSessionIdException: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF6A07F02E5+25029]
	(No symbol) [0x00007FF6A07452F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF6A0A6D73A+1947706]
	(No symbol) [0x00007FF6A050B790]
	(No symbol) [0x00007FF6A052A10A]
	(No symbol) [0x00007FF6A058ED27]
	(No symbol) [0x00007FF6A05A609A]
	(No symbol) [0x00007FF6A0588DF3]
	(No symbol) [0x00007FF6A055D6A6]
	(No symbol) [0x00007FF6A055CBB3]
	(No symbol) [0x00007FF6A055D4D3]
	(No symbol) [0x00007FF6A065638D]
	(No symbol) [0x00007FF6A0663F2F]
	(No symbol) [0x00007FF6A065C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF6A08D76EA+284650]
	(No symbol) [0x00007FF6A0752C81]
	(No symbol) [0x00007FF6A074B724]
	(No symbol) [0x00007FF6A074B873]
	(No symbol) [0x00007FF6A073D4B6]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:11:48.716617" elapsed="1.677557">InvalidSessionIdException: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF6A07F02E5+25029]
	(No symbol) [0x00007FF6A07452F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF6A0A6D73A+1947706]
	(No symbol) [0x00007FF6A050B790]
	(No symbol) [0x00007FF6A052A10A]
	(No symbol) [0x00007FF6A058ED27]
	(No symbol) [0x00007FF6A05A609A]
	(No symbol) [0x00007FF6A0588DF3]
	(No symbol) [0x00007FF6A055D6A6]
	(No symbol) [0x00007FF6A055CBB3]
	(No symbol) [0x00007FF6A055D4D3]
	(No symbol) [0x00007FF6A065638D]
	(No symbol) [0x00007FF6A0663F2F]
	(No symbol) [0x00007FF6A065C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF6A08D76EA+284650]
	(No symbol) [0x00007FF6A0752C81]
	(No symbol) [0x00007FF6A074B724]
	(No symbol) [0x00007FF6A074B873]
	(No symbol) [0x00007FF6A073D4B6]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<msg time="2025-06-21T09:11:50.394174" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T09:11:48.716617" elapsed="1.677557"/>
</kw>
<status status="PASS" start="2025-06-21T09:11:44.407484" elapsed="5.986690"/>
</iter>
<status status="PASS" start="2025-06-21T09:11:34.102784" elapsed="16.291390"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="FAIL" start="2025-06-21T09:11:50.394174" elapsed="0.000000">Execution terminated by signal</status>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-06-21T09:11:50.394174" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="FAIL" start="2025-06-21T09:11:00.753595" elapsed="49.640579">Execution terminated by signal</status>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="FAIL" start="2025-06-21T09:11:00.749701" elapsed="49.644473">Execution terminated by signal</status>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="FAIL" start="2025-06-21T09:11:00.722267" elapsed="49.671907">Execution terminated by signal</status>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T09:11:50.394174" elapsed="0.000000"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T09:11:50.394174" elapsed="0.000000"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<arg>${SEARCH_KEY}</arg>
<status status="NOT RUN" start="2025-06-21T09:11:50.394174" elapsed="0.000000"/>
</kw>
<kw name="Then The user verifies only one row is data present" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T09:11:50.394174" elapsed="0.000000"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="NOT RUN" start="2025-06-21T09:11:50.409849" elapsed="0.000000"/>
</kw>
<arg>Validate Search - Serial Number Coloumn- on ATM Details</arg>
<arg>VMS_PROD</arg>
<arg>43549618</arg>
<arg>Serial Number</arg>
<status status="FAIL" start="2025-06-21T09:11:00.706323" elapsed="49.703526">Execution terminated by signal</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T09:11:54.666115" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC11670&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:11:58.716230" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC12900&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:02.780156" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CB19AF0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:10.999637" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CAEC470&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:15.538055" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBCF290&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:19.613922" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBEE150&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:23.726291" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC25B50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T09:12:23.726291" level="FAIL">MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC5B560&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T09:11:50.409849" elapsed="33.593435">MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC5B560&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T09:12:24.003284" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T09:12:24.003284" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg time="2025-06-21T09:12:28.103084" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBB7200&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:32.187631" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBEDF40&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:36.268961" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBCEC00&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:44.549440" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC12750&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:48.615249" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC13F20&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:52.674409" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC135C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:56.746127" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CA82990&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T09:12:56.748395" level="FAIL">MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBFEC30&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="FAIL" start="2025-06-21T09:12:24.003284" elapsed="32.748161">MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBFEC30&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T09:12:59.751937" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T09:12:56.751445" elapsed="3.000492"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T09:13:03.818080" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CD7DC40&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
<msg time="2025-06-21T09:13:07.914450" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC25EB0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
<msg time="2025-06-21T09:13:12.014885" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC35340&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-21T09:12:59.751937" elapsed="16.327457"/>
</kw>
<status status="FAIL" start="2025-06-21T09:11:50.409849" elapsed="85.670549">Several failures occurred:

1) MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC5B560&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))

2) MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBFEC30&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</kw>
<doc>Validate Search - Serial Number Coloumn- on ATM Details</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-21T09:11:00.706323" elapsed="135.374075">Execution terminated by signal

Also teardown failed:
Several failures occurred:

1) MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC5B560&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))

2) MaxRetryError: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBFEC30&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</test>
<doc>Validate Search - Serial Number Coloumn- on ATM Details</doc>
<status status="FAIL" start="2025-06-21T09:11:00.167796" elapsed="135.913681"/>
</suite>
<status status="FAIL" start="2025-06-21T09:07:07.794495" elapsed="368.286982"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="3" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="2" fail="3" skip="0">ATM DETAILS</stat>
<stat pass="2" fail="3" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="2" fail="3" skip="0">VMS Portal</stat>
<stat name="RAC29a-TC-220 - Validate Accessing the ATM Details Screen- on ATM Details" id="s1-s1" pass="1" fail="0" skip="0">VMS Portal.RAC29a-TC-220 - Validate Accessing the ATM Details Screen- on ATM Details</stat>
<stat name="RAC29a-TC-221 - Validate Viewing ATM Device Details- on ATM Details" id="s1-s2" pass="0" fail="1" skip="0">VMS Portal.RAC29a-TC-221 - Validate Viewing ATM Device Details- on ATM Details</stat>
<stat name="RAC29a-TC-223 - Verify Last Update Date- on ATM Details" id="s1-s3" pass="0" fail="1" skip="0">VMS Portal.RAC29a-TC-223 - Verify Last Update Date- on ATM Details</stat>
<stat name="RAC29a-TC-224 Validate Search - ATM Number Column - on ATM Details" id="s1-s4" pass="1" fail="0" skip="0">VMS Portal.RAC29a-TC-224 Validate Search - ATM Number Column - on ATM Details</stat>
<stat name="RAC29a-TC-225 Validate Search - Serial Number Column - on ATM Details" id="s1-s5" pass="0" fail="1" skip="0">VMS Portal.RAC29a-TC-225 Validate Search - Serial Number Column - on ATM Details</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-21T09:07:07.777912" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:07:11.011768" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 312: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.011768" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 342: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.012767" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 373: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.012767" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 413: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.013767" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 458: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.013767" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 470: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.013767" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 523: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.015765" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 814: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.015765" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 845: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.015765" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 876: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.016770" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 912: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.016770" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 922: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.016770" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 933: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.018106" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 951: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.018106" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 976: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.019110" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 996: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.020110" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1023: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.020110" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1054: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.021573" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1160: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.022859" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1414: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.023288" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1525: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.054864" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.054864" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.142828" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.142828" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T09:07:11.731938" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:07:11.731938" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-21T09:07:12.633256" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-21T09:07:12.638794" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:07:32.187859" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:07:42.217998" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:07:47.255677" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:08.804413" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:08:09.794626" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-21T09:08:09.796021" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:08:25.199102" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:35.255954" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:08:40.279706" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:01.217502" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:09:02.153083" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-21T09:09:02.166081" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:09:19.937078" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:29.977345" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:35.009025" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:09:59.147714" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:10:00.409537" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-21T09:10:00.409537" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:10:17.061094" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:27.093026" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:32.128028" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:10:50.728908" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:00.626886" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T09:11:01.700623" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-21T09:11:01.708046" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T09:11:18.928321" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:28.977164" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:33.995161" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T09:11:50.346972" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Execution terminated by signal</msg>
<msg time="2025-06-21T09:11:54.666115" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC11670&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:11:58.716230" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC12900&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:02.780156" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CB19AF0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:10.999637" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CAEC470&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:15.538055" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBCF290&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:19.613922" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBEE150&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:23.726291" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC25B50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T09:12:28.103084" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBB7200&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:32.187631" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBEDF40&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:36.268961" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CBCEC00&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/elements</msg>
<msg time="2025-06-21T09:12:44.549440" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC12750&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:48.615249" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC13F20&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:52.674409" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC135C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0/screenshot</msg>
<msg time="2025-06-21T09:12:56.746127" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=64214): Max retries exceeded with url: /session/084d20919cdfaaf62a9fffa754afaec0/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CA82990&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T09:13:03.818080" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CD7DC40&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
<msg time="2025-06-21T09:13:07.914450" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC25EB0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
<msg time="2025-06-21T09:13:12.014885" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000001529CC35340&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/084d20919cdfaaf62a9fffa754afaec0</msg>
</errors>
</robot>
